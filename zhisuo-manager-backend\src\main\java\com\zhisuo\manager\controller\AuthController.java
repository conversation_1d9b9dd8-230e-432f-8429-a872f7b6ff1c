package com.zhisuo.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.zhisuo.manager.common.Result;
import com.zhisuo.manager.dto.LoginRequest;
import com.zhisuo.manager.dto.UserStatsDTO;
import com.zhisuo.manager.dto.UserProfileDTO;
import com.zhisuo.manager.entity.Admin;
import com.zhisuo.manager.service.AuthService;
import com.zhisuo.manager.vo.LoginResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    @Value("${jwt.header}")
    private String tokenHeader;
    
    @Value("${jwt.prefix}")
    private String tokenPrefix;
    
    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        try {
            LoginResponse response = authService.login(request);
            
            // 更新最后登录信息
            String clientIp = getClientIp(httpRequest);
            authService.updateLastLogin(response.getAdminInfo().getAdminId(), clientIp);
            
            return Result.success("登录成功", response);
        } catch (Exception e) {
            log.error("登录失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader(tokenHeader);
            if (StrUtil.isNotBlank(authHeader) && authHeader.startsWith(tokenPrefix)) {
                String token = authHeader.substring(tokenPrefix.length()).trim();
                authService.logout(token);
            }
            return Result.success("登出成功", null);
        } catch (Exception e) {
            log.error("登出失败", e);
            return Result.error("登出失败");
        }
    }
    
    /**
     * 获取当前用户完整信息（包含统计数据）
     */
    @GetMapping("/current")
    public Result<UserProfileDTO> getCurrentUser(HttpServletRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                String username = authentication.getName();

                // 通过用户名获取完整的用户信息
                Admin admin = authService.getAdminByUsername(username);
                if (admin == null) {
                    return Result.unauthorized();
                }

                // 获取用户统计数据
                UserStatsDTO stats = authService.getUserStats(username, request);

                // 构建完整的用户档案
                UserProfileDTO profile = new UserProfileDTO();

                // 基本信息
                profile.setAdminId(admin.getAdminId());
                profile.setUsername(admin.getUsername());
                profile.setRealName(admin.getRealName());
                profile.setEmail(admin.getEmail());
                profile.setPhone(admin.getPhone());
                profile.setAvatar(admin.getAvatar());
                profile.setRole(admin.getRole());
                // 根据role动态设置角色名称
                String roleName = "admin".equals(admin.getRole()) ? "超级管理员" : "普通管理员";
                profile.setRoleName(roleName);

                // 统计数据
                profile.setStats(stats);

                return Result.success("获取用户信息成功", profile);
            }
            return Result.unauthorized();
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败");
        }
    }
    
    /**
     * 获取用户统计数据（兼容性接口，建议使用 /current）
     * @deprecated 建议使用 /current 接口获取完整用户信息
     */
    @Deprecated
    @GetMapping("/stats")
    public Result<UserStatsDTO> getUserStats(HttpServletRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            UserStatsDTO stats = authService.getUserStats(username, request);
            return Result.success("获取统计数据成功", stats);
        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            return Result.error("获取统计数据失败");
        }
    }

    /**
     * 更新用户资料
     */
    @PutMapping("/profile")
    public Result<Void> updateProfile(@RequestBody Admin admin) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            authService.updateProfile(username, admin);
            return Result.success("更新资料成功", null);
        } catch (Exception e) {
            log.error("更新用户资料失败", e);
            return Result.error("更新资料失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public Result<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            authService.changePassword(username, request.getCurrentPassword(), request.getNewPassword());
            return Result.success("密码修改成功", null);
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码请求DTO
     */
    public static class ChangePasswordRequest {
        private String currentPassword;
        private String newPassword;

        public String getCurrentPassword() {
            return currentPassword;
        }

        public void setCurrentPassword(String currentPassword) {
            this.currentPassword = currentPassword;
        }

        public String getNewPassword() {
            return newPassword;
        }

        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
